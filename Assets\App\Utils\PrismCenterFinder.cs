using UnityEngine;
using System.Collections.Generic;
using System.Linq;

public class PrismCenterFinder : MonoBehaviour
{
    public Vector3 GetPrismCentroidWorld()
    {
        MeshFilter meshFilter = GetComponent<MeshFilter>();
        if (meshFilter == null || meshFilter.sharedMesh == null) return transform.position;

        Vector3[] vertices = meshFilter.sharedMesh.vertices;

        // Use a list to store unique vertex positions
        List<Vector3> uniqueVertices = new List<Vector3>();

        foreach (Vector3 v in vertices)
        {
            // Check if we already have this vertex (using a small epsilon for float precision)
            if (!uniqueVertices.Any(uv => Vector3.Distance(uv, v) < 0.001f))
            {
                uniqueVertices.Add(v);
            }
        }

        // Average the unique vertices
        Vector3 localCentroid = Vector3.zero;
        foreach (Vector3 uv in uniqueVertices)
        {
            localCentroid += uv;
        }
        localCentroid /= uniqueVertices.Count;

        // Return in World Space
        return transform.TransformPoint(localCentroid);
    }

    private void OnDrawGizmos()
    {
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireSphere(GetPrismCentroidWorld(), 0.05f);
    }
}