using ScriptableEvents.Events;
using UnityEngine;
using DG.Tweening;
using System;

public class CameraRig : MonoBehaviour
{
    [SerializeField] private MapManager mapManager;
    [SerializeField] private ElementScriptableEvent moveToStart;
    [SerializeField] private GameObject guideTopLeft;
    [SerializeField] private GameObject guideBottomRight;
    [SerializeField] private float moveAnimationDuration = 1f;
    [SerializeField] private float horizontalMargin = 1f;
    [SerializeField] private float topMargin = 1f;
    [SerializeField] private float bottomMargin = 1f;

    private Vector3 targetPosition;
    private float minX, maxX, minZ, maxZ;
    private Tweener currentTween; // To manage ongoing tweens

    void Awake()
    {
        moveToStart.AddListener((elem) =>
        {
            SetBounds();

            targetPosition = new Vector3(elem.Center.x, transform.position.y, elem.Center.z);

            if (currentTween != null && currentTween.IsActive())
            {
                currentTween.Kill();
                currentTween = null;
            }

            ClampToBounds(ref targetPosition);

            transform.position = targetPosition;
        });
    }

    private void SetBounds()
    {
        if (mapManager != null && mapManager.Map != null && mapManager.Map.Length > 0)
        {
            int rows = mapManager.Map.Length;
            int cols = mapManager.Map[0].Length;

            // Initialize bounds with the first element
            Vector3 firstCenter = mapManager.Map[0][0].GameObject.Center;
            Vector3 lastCenter = mapManager.Map[rows - 1][cols - 1].GameObject.Center;
            float mapMinX = firstCenter.x;
            float mapMaxX = lastCenter.x;
            float mapMinZ = lastCenter.z;
            float mapMaxZ = firstCenter.z;

            // Add 1f margin on both X and Z axes
            minX = mapMinX - horizontalMargin;
            maxX = mapMaxX + horizontalMargin;
            minZ = mapMinZ - bottomMargin;
            maxZ = mapMaxZ + topMargin;

            guideTopLeft.transform.position = new Vector3(minX, guideTopLeft.transform.position.y, maxZ);
            guideBottomRight.transform.position = new Vector3(maxX, guideBottomRight.transform.position.y, minZ);
        }
    }

    public void ApplyDragDelta(Vector2 delta)
    {
        if (delta != Vector2.zero)
        {
            targetPosition -= new Vector3(delta.x, transform.position.y, delta.y);

            ClampToBounds(ref targetPosition);
            MoveToPosition(targetPosition);
        }
    }

    private void ClampToBounds(ref Vector3 position)
    {
        position.x = Mathf.Clamp(position.x, minX, maxX);
        position.z = Mathf.Clamp(position.z, minZ, maxZ);
    }

    private void MoveToPosition(Vector3 position)
    {
        // Tween to position (used for non-drag movements like moveToStart)
        if (currentTween != null && currentTween.IsActive())
        {
            currentTween.ChangeEndValue(position, true);
        }
        else
        {
            currentTween = transform.DOMove(position, moveAnimationDuration).SetEase(Ease.OutCirc);
        }
    }
}
