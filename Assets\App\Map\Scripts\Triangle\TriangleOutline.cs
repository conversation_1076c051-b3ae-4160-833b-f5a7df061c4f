using UnityEngine;

public class TriangleOutline : MonoBehaviour
{
    [SerializeField] private Material possiblePath;
    [SerializeField] private Material onPath;
    [SerializeField] private MeshRenderer rendererComponent;

    void Awake()
    {
        Clear();
    }
    public void SetPossiblePath()
    {
        rendererComponent.enabled = true;
        rendererComponent.material = possiblePath;
    }

    public void SetOnPath()
    {
        rendererComponent.enabled = true;
        rendererComponent.material = onPath;
    }

    public void Clear()
    {
        rendererComponent.enabled = false;
    }
}
