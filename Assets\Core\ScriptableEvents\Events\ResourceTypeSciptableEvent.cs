using CHARK.ScriptableEvents;
using UnityEngine;

namespace ScriptableEvents.Events
{
    [CreateAssetMenu(
        fileName = "IResourceTypeScriptableEvent",
        menuName = ScriptableEventConstants.MenuNameCustom + "/Resource Type Scriptable Event",
        order = ScriptableEventConstants.MenuOrderCustom + 0
    )]
    internal sealed class ResourceTypeScriptableEvent : ScriptableEvent<ResourceType>
    {
    }
}
