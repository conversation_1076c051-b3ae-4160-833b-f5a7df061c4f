%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!43 &4300000
Mesh:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Triangle
  serializedVersion: 12
  m_SubMeshes:
  - serializedVersion: 2
    firstByte: 0
    indexCount: 36
    topology: 0
    baseVertex: 0
    firstVertex: 0
    vertexCount: 20
    localAABB:
      m_Center: {x: 0.24999997, y: 0.5, z: -0.000000029802322}
      m_Extent: {x: 0.75, y: 0.5, z: 0.86602545}
  m_Shapes:
    vertices: []
    shapes: []
    channels: []
    fullWeights: []
  m_BindPose: []
  m_BoneNameHashes: 
  m_RootBoneNameHash: 0
  m_BonesAABB: []
  m_VariableBoneCountWeights:
    m_Data: 
  m_MeshCompression: 0
  m_IsReadable: 1
  m_KeepVertices: 1
  m_KeepIndices: 1
  m_IndexFormat: 0
  m_IndexBuffer: 000001000200010003000200040005000600050007000600080009000a0009000b000a000c000d000e000f000d000c000e000d000f00100011001200120011001300130011001000
  m_VertexData:
    serializedVersion: 3
    m_VertexCount: 20
    m_Channels:
    - stream: 0
      offset: 0
      format: 0
      dimension: 3
    - stream: 0
      offset: 12
      format: 0
      dimension: 3
    - stream: 0
      offset: 24
      format: 0
      dimension: 4
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 40
      format: 0
      dimension: 2
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    - stream: 0
      offset: 0
      format: 0
      dimension: 0
    m_DataSize: 960
    _typelessdata: 0000803f00000000000000000000003f00000000d8b35d3fd7b35dbf00000000ffffff3e000080bfd8b35dbf000000000000803f0000803f000000000000003f00000000d8b35d3fd7b35dbf00000000ffffff3e000080bfd8b35dbf0000803f010000bf00000000d7b35d3f0000003f00000000d8b35d3fd7b35dbf00000000ffffff3e000080bfd8b35d3f00000000010000bf0000803fd7b35d3f0000003f00000000d8b35d3fd7b35dbf00000000ffffff3e000080bfd8b35d3f0000803f010000bf00000000d7b35d3f000080bf0000000088c0b8b388c0b83300000000000080bf000080bfd8b35dbf00000000010000bf0000803fd7b35d3f000080bf0000000088c0b8b388c0b83300000000000080bf000080bfd8b35dbf0000803ffdffffbe00000000d8b35dbf000080bf0000000088c0b8b388c0b83300000000000080bf000080bfd7b35d3f00000000fdffffbe0000803fd8b35dbf000080bf0000000088c0b8b388c0b83300000000000080bf000080bfd7b35d3f0000803ffdffffbe00000000d8b35dbf0200003f00000000d7b35dbfd7b35d3f000000000100003f000080bfd8b35dbf00000000fdffffbe0000803fd8b35dbf0200003f00000000d7b35dbfd7b35d3f000000000100003f000080bfd8b35dbf0000803f0000803f00000000000000000200003f00000000d7b35dbfd7b35d3f000000000100003f000080bfd7b35d3f000000000000803f0000803f000000000200003f00000000d7b35dbfd7b35d3f000000000100003f000080bfd7b35d3f0000803f010000bf00000000d7b35d3f00000000000080bf00000000000080bf0000000000000000000080bf0100003fd7b35d3f00000000000000000000000000000000000080bf00000000000080bf0000000000000000000080bf00000000000000000000803f000000000000000000000000000080bf00000000000080bf0000000000000000000080bf000080bf00000000fdffffbe00000000d8b35dbf00000000000080bf00000000000080bf0000000000000000000080bffdffff3ed8b35dbf0000803f0000803f00000000000000000000803f000000000000803f0000000000000000000080bf0000803f00000000000000000000803f00000000000000000000803f000000000000803f0000000000000000000080bf0000000000000000010000bf0000803fd7b35d3f000000000000803f000000000000803f0000000000000000000080bf010000bfd7b35d3ffdffffbe0000803fd8b35dbf000000000000803f000000000000803f0000000000000000000080bffdffffbed8b35dbf
  m_CompressedMesh:
    m_Vertices:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_UV:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Normals:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Tangents:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_Weights:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_NormalSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_TangentSigns:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_FloatColors:
      m_NumItems: 0
      m_Range: 0
      m_Start: 0
      m_Data: 
      m_BitSize: 0
    m_BoneIndices:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_Triangles:
      m_NumItems: 0
      m_Data: 
      m_BitSize: 0
    m_UVInfo: 0
  m_LocalAABB:
    m_Center: {x: 0.24999997, y: 0.5, z: -0.000000029802322}
    m_Extent: {x: 0.75, y: 0.5, z: 0.86602545}
  m_MeshUsageFlags: 0
  m_CookingOptions: 30
  m_BakedConvexCollisionMesh: 
  m_BakedTriangleCollisionMesh: 
  'm_MeshMetrics[0]': 1
  'm_MeshMetrics[1]': 1
  m_MeshOptimizationFlags: 1
  m_StreamData:
    serializedVersion: 2
    offset: 0
    size: 0
    path: 
  m_MeshLodInfo:
    serializedVersion: 2
    m_LodSelectionCurve:
      serializedVersion: 1
      m_LodSlope: 0
      m_LodBias: 0
    m_NumLevels: 1
    m_SubMeshes:
    - serializedVersion: 2
      m_Levels:
      - serializedVersion: 1
        m_IndexStart: 0
        m_IndexCount: 0
