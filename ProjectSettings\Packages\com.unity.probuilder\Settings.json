{"m_Dictionary": {"m_DictionaryValues": [{"type": "UnityEngine.ProBuilder.LogLevel, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "log.level", "value": "{\"m_Value\":3}"}, {"type": "UnityEngine.ProBuilder.LogOutput, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "log.output", "value": "{\"m_Value\":1}"}, {"type": "System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "log.path", "value": "{\"m_Value\":\"ProBuilderLog.txt\"}"}, {"type": "System.String, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "{\"m_Value\":\"Assets/ProBuilder Data/Default Material Palette.asset\"}"}, {"type": "UnityEngine.ProBuilder.SemVer, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "about.identifier", "value": "{\"m_Value\":{\"m_Major\":6,\"m_Minor\":0,\"m_Patch\":8,\"m_Build\":-1,\"m_Type\":\"\",\"m_Metadata\":\"\",\"m_Date\":\"\"}}"}, {"type": "UnityEngine.ProBuilder.SemVer, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "preferences.version", "value": "{\"m_Value\":{\"m_Major\":6,\"m_Minor\":0,\"m_Patch\":8,\"m_Build\":-1,\"m_Type\":\"\",\"m_Metadata\":\"\",\"m_Date\":\"\"}}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "experimental.enabled", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "mesh.newShapesSnapToGrid", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "mesh.meshColliderIsConvex", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "ShapeComponent.ResetSettings", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "ShapeComponent.SettingsEnabled", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "lightmapping.autoUnwrapLightmapUV", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.autoRecalculateCollisions", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "smoothing.showSettings", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "smoothing.showPreview", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "smoothing.showNormals", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "smoothing.showHelp", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.backFaceSelectEnabled", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.showSceneInfo", "value": "{\"m_Value\":false}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "meshImporter.quads", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "meshImporter.smoothing", "value": "{\"m_Value\":true}"}, {"type": "<PERSON>.<PERSON>, mscorl<PERSON>, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "editor.autoUpdatePreview", "value": "{\"m_Value\":false}"}, {"type": "UnityEngine.ProBuilder.SelectMode, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "s_SelectMode", "value": "{\"m_Value\":1}"}, {"type": "UnityEngine.Rendering.ShadowCastingMode, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "mesh.shadowCastingMode", "value": "{\"m_Value\":1}"}, {"type": "UnityEngine.Material, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "mesh.userMaterial", "value": "{\"m_Value\":{\"instanceID\":0}}"}, {"type": "UnityEditor.StaticEditorFlags, UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "mesh.defaultStaticEditorFlags", "value": "{\"m_Value\":0}"}, {"type": "UnityEngine.ProBuilder.ColliderType, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "mesh.newShapeColliderType", "value": "{\"m_Value\":2}"}, {"type": "UnityEngine.ProBuilder.UnwrapParameters, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "lightmapping.defaultLightmapUnwrapParameters", "value": "{\"m_Value\":{\"m_HardAngle\":88.0,\"m_PackMargin\":20.0,\"m_AngleError\":8.0,\"m_AreaError\":15.0}}"}, {"type": "System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "ShapeBuilder.ActiveShapeIndex", "value": "{\"m_Value\":7}"}, {"type": "UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastSize.Cone", "value": "{\"m_Value\":{\"x\":4.0,\"y\":2.053741455078125,\"z\":2.999999761581421}}"}, {"type": "UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastSize.Torus", "value": "{\"m_Value\":{\"x\":-2.0,\"y\":1.350252389907837,\"z\":2.0}}"}, {"type": "UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastSize.Cylinder", "value": "{\"m_Value\":{\"x\":1.0000001192092896,\"y\":0.8740875124931336,\"z\":-2.0}}"}, {"type": "UnityEngine.Vector3, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastSize.Cube", "value": "{\"m_Value\":{\"x\":1.0,\"y\":1.0,\"z\":1.0}}"}, {"type": "UnityEngine.Quaternion, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastRotation.Cone", "value": "{\"m_Value\":{\"x\":0.0,\"y\":0.0,\"z\":0.0,\"w\":1.0}}"}, {"type": "UnityEngine.Quaternion, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastRotation.Torus", "value": "{\"m_Value\":{\"x\":0.0,\"y\":0.0,\"z\":0.0,\"w\":1.0}}"}, {"type": "UnityEngine.Quaternion, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastRotation.Cylinder", "value": "{\"m_Value\":{\"x\":-0.7071067690849304,\"y\":0.0,\"z\":0.0,\"w\":0.7071067690849304}}"}, {"type": "UnityEngine.Quaternion, UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.LastRotation.Cube", "value": "{\"m_Value\":{\"x\":0.0,\"y\":0.0,\"z\":0.0,\"w\":1.0}}"}, {"type": "UnityEngine.ProBuilder.PivotLocation, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.PivotLocation.Cone", "value": "{\"m_Value\":0}"}, {"type": "UnityEngine.ProBuilder.PivotLocation, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.PivotLocation.Torus", "value": "{\"m_Value\":0}"}, {"type": "UnityEngine.ProBuilder.PivotLocation, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.PivotLocation.Cylinder", "value": "{\"m_Value\":0}"}, {"type": "UnityEngine.ProBuilder.Shapes.Shape, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.Cone", "value": "{}"}, {"type": "UnityEngine.ProBuilder.Shapes.Shape, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.Torus", "value": "{}"}, {"type": "UnityEngine.ProBuilder.Shapes.Shape, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "ShapeBuilder.Cylinder", "value": "{}"}, {"type": "UnityEngine.ProBuilder.RectSelectMode, Unity.ProBuilder, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "key": "editor.dragSelectRectMode", "value": "{\"m_Value\":0}"}, {"type": "System.Single, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", "key": "meshImporter.smoothingAngle", "value": "{\"m_Value\":1.0}"}]}}