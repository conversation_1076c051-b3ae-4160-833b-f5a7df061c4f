using System;

public enum ResourceType
{
    Wood,
    Stone,
    Water,
}

public static class ResourceTypeExtensions
{
    public static Array Values = Enum.GetValues(typeof(ResourceType));

    public static ResourceType GetRandom()
    {
        return (ResourceType)Values.GetValue(UnityEngine.Random.Range(0, Values.Length));
    }

    /// <summary>
    /// Returns a random resource type where the given resource has 20% chance
    /// and the other two resources have 40% chance each.
    /// </summary>
    /// <param name="givenResource">The resource type that should have 20% probability</param>
    /// <returns>A random resource type with weighted probabilities</returns>
    public static ResourceType GetRandomWeighted(ResourceType givenResource)
    {
        float randomValue = UnityEngine.Random.Range(0f, 1f);

        // 20% chance for the given resource
        if (randomValue < 0.2f)
        {
            return givenResource;
        }

        // 40% chance each for the other two resources
        ResourceType[] otherResources = GetOtherResources(givenResource);

        // 20% to 60% range for first other resource (40% chance)
        if (randomValue < 0.6f)
        {
            return otherResources[0];
        }

        // 60% to 100% range for second other resource (40% chance)
        return otherResources[1];
    }

    /// <summary>
    /// Helper method to get the other two resource types excluding the given one.
    /// </summary>
    /// <param name="excludeResource">The resource type to exclude</param>
    /// <returns>Array of the other two resource types</returns>
    private static ResourceType[] GetOtherResources(ResourceType excludeResource)
    {
        switch (excludeResource)
        {
            case ResourceType.Wood:
                return new ResourceType[] { ResourceType.Stone, ResourceType.Water };
            case ResourceType.Stone:
                return new ResourceType[] { ResourceType.Wood, ResourceType.Water };
            case ResourceType.Water:
                return new ResourceType[] { ResourceType.Wood, ResourceType.Stone };
            default:
                throw new ArgumentException($"Unknown resource type: {excludeResource}");
        }
    }

}
