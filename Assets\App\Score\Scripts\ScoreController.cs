using System;
using CHARK.ScriptableEvents.Events;
using ScriptableEvents.Events;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;

public class ScoreController : MonoBehaviour
{
    [FormerlySerializedAs("scoreText")]
    [SerializeField] private TextMeshProUGUI textUi;
    [SerializeField] private IntScriptableEvent scoreUpdated;
    public int Score { get; private set; } = 0;

    public void Reset()
    {
        Score = 0;
        UpdateScore();
    }

    public void AddScore(int amount)
    {
        Score += CalculateAmountEarned(amount);
        UpdateScore();
    }

    private static int CalculateAmountEarned(int value)
    {
        int score = value switch
        {
            1 => 10,
            2 => 20,
            _ => 10 + (int)Math.Round(Math.Round((12 + value) * Math.Pow(value - 1, 0.5f + value * 0.1f))),
        };

        return score;
    }

    // Update is called once per frame
    void UpdateScore()
    {
        textUi.text = $"Score: {Score}";
        scoreUpdated.Raise(Score);
    }
}
