using System;
using CHARK.ScriptableEvents.Events;
using ScriptableEvents.Events;
using TMPro;
using UnityEngine;

public class TurnController : MonoBehaviour
{
    [SerializeField] private int totalTurns;
    [SerializeField] private TextMeshProUGUI textUi;
    [SerializeField] private ElementScriptableEvent moveToStart;
    [SerializeField] private SimpleScriptableEvent moveCompleted;
    [SerializeField] private StringScriptableEvent endGame;

    private int currentTurnNo = 1;
    // Start is called once before the first execution of Update after the MonoBehaviour is created
    void Awake()
    {
        moveToStart.AddListener((elem) =>
        {
            currentTurnNo = 1;
            UpdateTurn();
        });

        moveCompleted.AddListener((data) =>
        {
            if (currentTurnNo == totalTurns)
            {
                endGame.Raise("turns over");
                return;
            }

            currentTurnNo++;
            UpdateTurn();
        });
    }

    void UpdateTurn()
    {
        textUi.text = $"Turn<br>{currentTurnNo}/{totalTurns}";
    }
}
