using UnityEngine;
using UnityEngine.EventSystems;

public class CameraPlaneController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDown<PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IPointerUpHandler
{
    [SerializeField] private CameraRig cameraRig; // Reference to CameraRig for movement
    [SerializeField] private float dragThreshold = 0.2f; // Minimum pixels to start dragging
    [SerializeField] private float moveSpeed = 0.5f;

    private bool isDragging = false;
    private Vector2 dragStartPosition;
    private Vector2 lastPointerPosition;

    public void OnPointerDown(PointerEventData eventData)
    {
        // Start potential drag
        isDragging = false;
        dragStartPosition = eventData.position;
        lastPointerPosition = eventData.position;
    }

    public void OnDrag(PointerEventData eventData)
    {
        Vector2 currentPosition = eventData.position;
        Vector2 deltaFromStart = currentPosition - dragStartPosition;

        if (!isDragging && deltaFromStart.magnitude > dragThreshold)
        {
            isDragging = true;
        }

        if (isDragging)
        {
            Vector2 delta = currentPosition - lastPointerPosition;
            lastPointerPosition = currentPosition;

            // Apply to CameraRig's target position
            cameraRig.ApplyDragDelta(delta * moveSpeed);
        }
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        isDragging = false;
    }
}