public class MapElement
{
    public ITriangle GameObject { get; private set; }
    public int Row { get; private set; }
    public int Column { get; private set; }

    public MapElement(ITriangle gameObject, int row, int column)
    {
        GameObject = gameObject;
        Row = row;
        Column = column;
    }

    public override string ToString()
    {
        return $"Name: {GameObject.Name}, Resource: {GameObject.Resource}, State: {GameObject.Highlight}";
    }
}
