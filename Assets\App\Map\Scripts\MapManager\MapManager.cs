using UnityEngine;
using Random = System.Random;
using Debug = UnityEngine.Debug;
using System.Collections.Generic;

public class MapManager : MonoBehaviour
{
    [SerializeField] private GameObject container;
    [SerializeField] private Triangle elementPrefab;
    [SerializeField] private float centerOffset;
    [SerializeField] private float columnSpacing;
    [SerializeField] private float rowSpacing;
    public MapElement StartElement { get; private set; }
    public MapElement[][] Map { get; private set; }

    void Start()
    {
        Debug.Log("Map Manager is active!");
    }

    public void Create(int rows = 10, int cols = 17)
    {
        Map = new MapElement[rows][];

        for (int i = 0; i < Map.Length; i++)
        {
            Map[i] = new MapElement[cols];
            //initialize each element
            for (int j = 0; j < Map[i].Length; j++)
            {
                Map[i][j] = CreateElement(i, j, rows);
            }
        }

        AssignStartElement();
    }

    public void Restart()
    {
        foreach (var row in Map)
        {
            foreach (var elem in row)
            {
                elem.GameObject.Refresh();
            }
        }

        AssignStartElement();
    }
    private void AssignStartElement()
    {
        Random random = new();
        var sx = random.Next(2, Map.Length - 2);
        var sy = random.Next(2, Map[0].Length - 2);

        StartElement = Map[sx][sy];
    }
    private MapElement CreateElement(int row, int column, int totalRows)
    {
        bool isPointUp = IsElementPointedUp(row, column);
        // Base position
        float x = column * columnSpacing;

        // Calculate Z so that the last row (Map.Length - 1) is at Z = 0
        // and earlier rows extend in positive Z direction
        float z = (totalRows - 1 - row) * rowSpacing;

        if (!isPointUp)
        {
            z += centerOffset;
        }

        // Instantiate the element prefab as a child of this MapManager
        var instance = Instantiate(elementPrefab, container.transform);
        instance.name = $"Element_{row}_{column}";
        instance.transform.localPosition = new Vector3(x, 0, z);
        // Rotate point-down elements around Y by 180 degrees
        instance.transform.localRotation = isPointUp ? Quaternion.identity : Quaternion.Euler(0f, 180f, 0f);

        return new MapElement(instance, row, column);
    }

    public MapElement FindByName(string name)
    {
        foreach (var row in Map)
        {
            foreach (var item in row)
            {
                if (item.GameObject.Name == name) return item;
            }
        }
        return null;
    }

    /// <summary>
    /// Determines if an element at the given position is pointed up or down.
    /// Pattern:
    /// - Row 0: up, down, up, down...
    /// - Row 1: down, up, down, up...
    /// - Row 2: up, down, up, down... (same as row 0)
    /// - And so on...
    /// </summary>
    /// <param name="row">The row index (0-based)</param>
    /// <param name="column">The column index (0-based)</param>
    /// <returns>True if the element is pointed up, false if pointed down</returns>
    public static bool IsElementPointedUp(int row, int column)
    {
        return (row + column) % 2 == 0;
    }

    /// <summary>
    /// Determines if an element at the given position is pointed up or down.
    /// </summary>
    /// <param name="position">The element position</param>
    /// <returns>True if the element is pointed up, false if pointed down</returns>
    public static bool IsElementPointedUp(ElementPosition position)
    {
        return IsElementPointedUp(position.row, position.column);
    }

    private void PrintMap()
    {
        Debug.Log("\n");
        for (int i = 0; i < Map.Length; i++)
        {
            var row = "";
            foreach (var item in Map[i])
            {
                row += item + " ";
            }
            //for (int j = 0; j < currentMap[i].Length; j++)
            //{
            //    Debug.Log((currentMap[i][j].type) + " ");
            //}
            Debug.Log($"{row}\n");
        }
    }

    public List<MapElement> GetNeighbors(MapElement mapElement)
    {
        List<MapElement> neighbors = new();
        int row = mapElement.Row; // Assuming MapElement has public int Row { get; }
        int col = mapElement.Column; // Assuming MapElement has public int Column { get; }

        // Left neighbor (same row)
        if (col > 0)
        {
            neighbors.Add(Map[row][col - 1]);
        }

        // Right neighbor (same row)
        if (col < Map[row].Length - 1)
        {
            neighbors.Add(Map[row][col + 1]);
        }

        // Determine if pointed up or down
        bool isUp = IsElementPointedUp(row, col);

        if (isUp)
        {
            // Row below (same column)
            if (row < Map.Length - 1)
            {
                neighbors.Add(Map[row + 1][col]);
            }
        }
        else
        {
            // Row above (same column)
            if (row > 0)
            {
                neighbors.Add(Map[row - 1][col]);
            }
        }

        return neighbors;
    }
}