using System.Collections.Generic;
using CHARK.ScriptableEvents.Events;
using UnityEngine;
using DG.Tweening;
using ScriptableEvents.Events;

public class PathController : MonoBehaviour
{
    [SerializeField] private MapManager mapManager;
    [SerializeField] private ElementScriptableEvent moveToStart;
    [SerializeField] private ElementScriptableEvent elementSelected;
    [SerializeField] private SimpleScriptableEvent onMove;
    [SerializeField] private SimpleScriptableEvent moveCompleted;
    [SerializeField] private SimpleScriptableEvent moveRejected;
    [SerializeField] private ResourceTypeScriptableEvent resourceGathered;
    [SerializeField] private IntScriptableEvent amountGathered;
    [SerializeField] private StringScriptableEvent endGame;
    [SerializeField] private GameObject player;
    [SerializeField] private GameObject pathTarget;

    private readonly List<ITriangle> path = new();

    private ITriangle Current { get; set; }

    void Awake()
    {
        elementSelected.AddListener((elem) =>
        {
            Debug.Log("Selected element! " + elem);

            DeactivateNeighbors(Current);

            elem.Highlight = HighlightType.OnPath;

            path.Add(elem);
            pathTarget.transform.position = new Vector3(elem.Center.x, pathTarget.transform.position.y, elem.Center.z);

            Current = elem;

            ActivateNeighbors(Current);
        });

        moveToStart.AddListener((elem) =>
        {
            MoveToStart(elem);
        });

        onMove.AddListener((data) =>
        {
            Debug.Log("Move!");

            if (path.Count == 0)
            {
                moveRejected.Raise();
                Debug.Log("Move rejected!");

                return;
            }

            amountGathered.Raise(path.Count);
            MoveToNext(0);
        });
    }

    public void MoveToStart(ITriangle tri)
    {
        path.Clear();

        pathTarget.transform.position = new Vector3(tri.Center.x, pathTarget.transform.position.y, tri.Center.z);
        player.transform.position = new Vector3(tri.Center.x, player.transform.position.y, tri.Center.z);

        Current = tri;
        ActivateNeighbors(Current, false);
    }

    private void MoveToNext(int index)
    {
        if (index >= path.Count) // If we've reached the end of the path
        {
            Debug.Log("Move completed!");

            path.Clear(); // Clear the path after all objects are processed
            moveCompleted.Raise(); // Trigger the onMoveCompleted event
            int activeNeighbors = ActivateNeighbors(Current, false);
            if (activeNeighbors == 0)
            {
                endGame.Raise("blocked");
            }
            return;
        }

        Current = path[index];

        Vector3 targetPosition = new(
            Current.Center.x,
            player.transform.position.y,
            Current.Center.z
        );

        player.transform.DOMove(targetPosition, 0.3f).OnComplete(() =>
        {
            Current.Highlight = HighlightType.None; // Deselect the current GameObject
            Current.State = ElementState.Gathered;
            resourceGathered.Raise(Current.Resource);
            MoveToNext(index + 1); // Move to the next GameObject
        });
    }

    public int ActivateNeighbors(ITriangle tri, bool withSameResource = true)
    {
        MapElement elem = mapManager.FindByName(tri.Name);
        List<MapElement> findValidNeighbors = mapManager
            .GetNeighbors(elem)
            .FindAll(n =>
                {
                    if (n == Current) return false;
                    if (path.Contains(n.GameObject)) return false;

                    if (withSameResource && n.GameObject.Resource == tri.Resource && n.GameObject.State == ElementState.Walkable)
                    {
                        return true;
                    }

                    if (!withSameResource && n.GameObject.State == ElementState.Walkable)
                    {
                        return true;
                    }

                    return false;
                }
            );

        findValidNeighbors.ForEach(n =>
        {
            n.GameObject.Highlight = HighlightType.PossiblePath;
        });

        return findValidNeighbors.Count;
    }

    public void DeactivateNeighbors(ITriangle tri)
    {
        MapElement elem = mapManager.FindByName(tri.Name);
        mapManager.GetNeighbors(elem)
            .ForEach(n =>
            {
                if (n == Current) return;
                if (path.Contains(n.GameObject)) return;

                n.GameObject.Highlight = HighlightType.None;
            });
    }
}
