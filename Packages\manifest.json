{"dependencies": {"com.chark.scriptable-events": "https://github.com/chark/scriptable-events.git#upm", "com.unity.2d.enhancers": "1.0.0", "com.unity.ai.assistant": "1.5.0-pre.2", "com.unity.ai.generators": "1.0.0-pre.20", "com.unity.ai.inference": "2.4.1", "com.unity.ai.navigation": "2.0.9", "com.unity.collab-proxy": "2.11.2", "com.unity.ide.rider": "3.0.38", "com.unity.ide.visualstudio": "2.0.26", "com.unity.inputsystem": "1.17.0", "com.unity.multiplayer.center": "1.0.1", "com.unity.probuilder": "6.0.8", "com.unity.render-pipelines.universal": "17.3.0", "com.unity.test-framework": "1.6.0", "com.unity.timeline": "1.8.10", "com.unity.ugui": "2.0.0", "com.unity.visualscripting": "1.9.9", "com.unity.modules.accessibility": "1.0.0", "com.unity.modules.adaptiveperformance": "1.0.0", "com.unity.modules.ai": "1.0.0", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.cloth": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.screencapture": "1.0.0", "com.unity.modules.terrain": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.umbra": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.unitywebrequesttexture": "1.0.0", "com.unity.modules.unitywebrequestwww": "1.0.0", "com.unity.modules.vectorgraphics": "1.0.0", "com.unity.modules.vehicles": "1.0.0", "com.unity.modules.video": "1.0.0", "com.unity.modules.vr": "1.0.0", "com.unity.modules.wind": "1.0.0", "com.unity.modules.xr": "1.0.0"}}