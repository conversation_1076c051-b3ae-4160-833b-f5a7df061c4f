using CHARK.ScriptableEvents.Events;
using UnityEngine;
using System;
using ScriptableEvents.Events;

public class MapController : MonoBehaviour
{
    [SerializeField] private MapManager mapManager;
    [SerializeField] private SimpleScriptableEvent onMoveCompleted;
    [SerializeField] private ElementScriptableEvent moveToStart;
    [SerializeField] private SimpleScriptableEvent newGame;

    void Awake()
    {
        onMoveCompleted.AddListener((data) =>
        {
            UpdateTriangles();
        });

        newGame.AddListener((data) =>
        {
            mapManager.Restart();
            NewGame();
        });
    }

    void Start()
    {
        mapManager.Create();
        newGame.Raise();
    }

    void NewGame()
    {
        MapElement start = mapManager.StartElement;
        start.GameObject.State = ElementState.Gathered;

        moveToStart.Raise(start.GameObject);
    }

    void UpdateTriangles()
    {
        foreach (var row in mapManager.Map)
        {
            foreach (var elem in row)
            {
                ITriangle tri = elem.GameObject;

                if (tri.State == ElementState.Walkable) continue;
                if (tri.State == ElementState.Blocked) continue;

                switch (tri.State)
                {
                    case ElementState.Gathered:
                        tri.State = ElementState.Regenerating;
                        break;
                    case ElementState.Regenerating:
                        switch (tri.Regeneration)
                        {
                            case RegenerationType.None:
                                tri.Regeneration = RegenerationType.Almost;
                                break;
                            case RegenerationType.Almost:
                                tri.Regeneration = RegenerationType.Some;
                                break;
                            case RegenerationType.Some:
                                tri.Regeneration = RegenerationType.Completed;
                                tri.State = ElementState.Walkable;
                                break;
                        }
                        break;
                }
            }
        }
    }
}
