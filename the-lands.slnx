﻿<Solution>
  <Project Path="Unity.Timeline.csproj" />
  <Project Path="Unity.PlasticSCM.Editor.csproj" />
  <Project Path="Unity.AppUI.csproj" />
  <Project Path="Unity.ProBuilder.csproj" />
  <Project Path="Unity.AI.ModelSelector.csproj" />
  <Project Path="Unity.Mathematics.csproj" />
  <Project Path="Unity.Timeline.Editor.csproj" />
  <Project Path="Unity.Collections.csproj" />
  <Project Path="Unity.ProBuilder.Poly2Tri.csproj" />
  <Project Path="Unity.InferenceEngine.Microsoft.Msagl.csproj" />
  <Project Path="CHARK.ScriptableEvents.Editor.csproj" />
  <Project Path="Unity.VisualScripting.Core.Editor.csproj" />
  <Project Path="Unity.PerformanceTesting.Editor.csproj" />
  <Project Path="Unity.AppUI.Redux.csproj" />
  <Project Path="Unity.Burst.csproj" />
  <Project Path="Unity.AI.ModelTrainer.csproj" />
  <Project Path="Unity.InferenceEngine.Editor.Visualizer.csproj" />
  <Project Path="Unity.AI.Material.csproj" />
  <Project Path="Unity.VisualScripting.Flow.Editor.csproj" />
  <Project Path="Unity.ProBuilder.Csg.csproj" />
  <Project Path="Unity.VisualStudio.Editor.csproj" />
  <Project Path="Unity.InputSystem.csproj" />
  <Project Path="Unity.AI.Animate.csproj" />
  <Project Path="Unity.AI.Toolkit.Accounts.csproj" />
  <Project Path="Unity.InferenceEngine.csproj" />
  <Project Path="Unity.Searcher.Editor.csproj" />
  <Project Path="Unity.AI.Generators.Redux.csproj" />
  <Project Path="Unity.ProBuilder.Editor.csproj" />
  <Project Path="Unity.AI.Sound.csproj" />
  <Project Path="Unity.InferenceEngine.Tokenization.csproj" />
  <Project Path="Unity.VisualScripting.Core.csproj" />
  <Project Path="Unity.InferenceEngine.Editor.csproj" />
  <Project Path="Unity.AI.Navigation.Editor.ConversionSystem.csproj" />
  <Project Path="Unity.VisualScripting.State.Editor.csproj" />
  <Project Path="Unity.Burst.CodeGen.csproj" />
  <Project Path="Assembly-CSharp.csproj" />
  <Project Path="Unity.VisualScripting.Flow.csproj" />
  <Project Path="Unity.PerformanceTesting.csproj" />
  <Project Path="Unity.AI.Image.csproj" />
  <Project Path="CHARK.ScriptableEvents.csproj" />
  <Project Path="Unity.2D.Enhancers.Editor.csproj" />
  <Project Path="Unity.AppUI.Navigation.Editor.csproj" />
  <Project Path="Unity.InternalAPIEngineBridge.001.csproj" />
  <Project Path="Unity.Burst.Editor.csproj" />
  <Project Path="Unity.2D.Common.Path.Editor.csproj" />
  <Project Path="Unity.2D.Common.Editor.csproj" />
  <Project Path="ScriptablePacker.csproj" />
  <Project Path="Unity.Rider.Editor.csproj" />
  <Project Path="Unity.VisualScripting.State.csproj" />
  <Project Path="Unity.InternalAPIEditorBridge.001.csproj" />
  <Project Path="Unity.AI.Navigation.Updater.csproj" />
  <Project Path="Unity.AI.Generators.Sdk.csproj" />
  <Project Path="Unity.AI.Navigation.Editor.csproj" />
  <Project Path="Unity.AppUI.MVVM.csproj" />
  <Project Path="Assembly-CSharp-firstpass.csproj" />
  <Project Path="Unity.AI.Generators.UI.csproj" />
  <Project Path="Unity.ProBuilder.KdTree.csproj" />
  <Project Path="Unity.ProBuilder.AddOns.Editor.csproj" />
  <Project Path="Unity.AppUI.Navigation.csproj" />
  <Project Path="Unity.InputSystem.ForUI.csproj" />
  <Project Path="Unity.AI.Navigation.csproj" />
  <Project Path="Unity.Collections.CodeGen.csproj" />
  <Project Path="Unity.Mathematics.Editor.csproj" />
  <Project Path="Unity.VisualScripting.SettingsProvider.Editor.csproj" />
  <Project Path="Unity.AI.Toolkit.GenerationContextMenu.csproj" />
  <Project Path="Unity.2D.Common.Runtime.csproj" />
  <Project Path="Unity.AI.Generators.UI.AIDropdownIntegrations.csproj" />
  <Project Path="Unity.AI.Material.Srp.csproj" />
  <Project Path="Unity.ProBuilder.AssetIdRemapUtility.csproj" />
  <Project Path="Unity.AppUI.Editor.csproj" />
  <Project Path="Unity.Settings.Editor.csproj" />
  <Project Path="Unity.InputSystem.TestFramework.csproj" />
  <Project Path="Unity.AI.Toolkit.Async.csproj" />
  <Project Path="Unity.VisualScripting.Shared.Editor.csproj" />
  <Project Path="Unity.AppUI.Redux.Editor.csproj" />
  <Project Path="Unity.AI.Toolkit.Compliance.csproj" />
  <Project Path="Unity.2D.Enhancers.Editor.AIBridge.csproj" />
  <Project Path="Unity.AppUI.Undo.csproj" />
  <Project Path="Unity.AI.Generators.Contexts.csproj" />
  <Project Path="Unity.AI.Generators.Asset.csproj" />
  <Project Path="Unity.AI.Generators.Chat.csproj" />
  <Project Path="Unity.CollabProxy.Editor.csproj" />
  <Project Path="Unity.AI.Toolkit.Asset.csproj" />
  <Project Path="Unity.AI.Animate.Motion.Runtime.csproj" />
  <Project Path="Unity.InputSystem.DocCodeSamples.csproj" />
  <Project Path="Unity.InferenceEngine.MacBLAS.csproj" />
  <Project Path="Unity.ProBuilder.Stl.csproj" />
  <Project Path="Unity.AppUI.InternalAPIBridge.csproj" />
  <Project Path="Unity.AI.Toolkit.GenerationObjectPicker.csproj" />
  <Project Path="Unity.AI.Toolkit.Chat.csproj" />
  <Project Path="Unity.InferenceEngine.ONNX.Editor.csproj" />
  <Project Path="Unity.VisualScripting.DocCodeExamples.csproj" />
  <Project Path="Unity.InferenceEngine.iOSBLAS.csproj" />
  <Project Path="Unity.PlasticSCM.Editor.Entities.csproj" />
  <Project Path="Unity.Collections.Editor.csproj" />
</Solution>
