using CHARK.ScriptableEvents;
using UnityEngine;

namespace ScriptableEvents.Events
{
    [CreateAssetMenu(
        fileName = "IElementScriptableEvent",
        menuName = ScriptableEventConstants.MenuNameCustom + "/Element Scriptable Event",
        order = ScriptableEventConstants.MenuOrderCustom + 0
    )]
    internal sealed class ElementScriptableEvent : ScriptableEvent<ITriangle>
    {
    }
}
