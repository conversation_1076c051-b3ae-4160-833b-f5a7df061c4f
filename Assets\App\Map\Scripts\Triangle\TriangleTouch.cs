using UnityEngine;
using UnityEngine.EventSystems;

public class TriangleTouch : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler
{
    [SerializeField] private MeshRenderer rendererComponent;

    void Awake()
    {
        rendererComponent.materials = new Material[] { };
    }

    public delegate void OnInteract();
    private OnInteract onInteract;

    public void AddOnInteract(OnInteract onInteract)
    {
        this.onInteract += onInteract;
    }
    public void RemoveOnInteract(OnInteract onInteract)
    {
        this.onInteract -= onInteract;
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        Debug.Log("You just tapped an element");
        onInteract?.Invoke();
    }
}
