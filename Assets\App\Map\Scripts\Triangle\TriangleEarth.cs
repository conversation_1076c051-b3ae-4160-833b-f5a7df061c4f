using System;
using UnityEngine;

public class TriangleEarth : MonoBehaviour
{
    [SerializeField] private Material wood;
    [SerializeField] private Material stone;
    [SerializeField] private Material water;
    [SerializeField] private Material fallback;
    [SerializeField] private MeshRenderer rendererComponent;

    private Material activeMaterial;
    private ResourceType activeResource = (ResourceType)(-1); // Invalid default
    private Color activeMaterialColor;
    private MaterialPropertyBlock propertyBlock;
    private RegenerationType _regeneration = RegenerationType.Completed;

    public RegenerationType Regeneration
    {
        get => _regeneration;
        set
        {
            if (_regeneration == value) return;
            _regeneration = value;

            UpdateMaterialColor();
        }
    }

    public void PickRandomResource()
    {
        Resource = ResourceTypeExtensions.GetRandom();
    }

    void Awake()
    {
        propertyBlock = new MaterialPropertyBlock();
        PickRandomResource();
    }

    public ResourceType Resource
    {
        get
        {
            return activeResource;
        }
        set
        {
            if (activeResource == value) return;

            activeResource = value;

            Material newMaterial = activeResource switch
            {
                ResourceType.Wood => wood,
                ResourceType.Stone => stone,
                ResourceType.Water => water,
                _ => fallback,
            };

            // Use this if you want to use unique materials per instance
            /* activeMaterial = Instantiate(newMaterial);
            activeMaterialColor = activeMaterial.color;
            rendererComponent.materials = new Material[] { activeMaterial }; */

            activeMaterial = newMaterial; // Use shared material
            activeMaterialColor = activeMaterial.GetColor("_BaseColor"); // Get from shared
            rendererComponent.sharedMaterial = activeMaterial; // Assign shared

            UpdateMaterialColor();
        }
    }

    private void UpdateMaterialColor()
    {
        if (activeMaterial == null) return;

        /* List<float> values = new() { 0.33f, 0.66f, 1f }; */

        /* activeMaterial.color = Color.Lerp(Color.black, activeMaterialColor, values[UnityEngine.Random.Range(0, values.Count)]); */

        float value = Regeneration switch
        {
            RegenerationType.None => 0f,
            RegenerationType.Almost => 0.33f,
            RegenerationType.Some => 0.66f,
            RegenerationType.Completed => 1f,
            _ => throw new NotImplementedException()
        };

        Color lerpedColor = Color.Lerp(Color.black, activeMaterialColor, value);
        propertyBlock.SetColor("_BaseColor", lerpedColor);
        rendererComponent.SetPropertyBlock(propertyBlock); // Apply override
    }
}