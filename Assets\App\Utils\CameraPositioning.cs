using System;
using UnityEngine;

[ExecuteInEditMode]
[RequireComponent(typeof(Camera))]
public class CameraPositioning : MonoBehaviour
{
    [SerializeField] private float topBuffer = 5f; // Top buffer distance in world units

    private Camera cameraComponent;

    void Awake()
    {
        cameraComponent = GetComponent<Camera>();

        if (cameraComponent == null)
            throw new Exception("Camera component not found on " + name);
    }

    // This will be called when values change in the inspector during edit mode
    /*
    void OnValidate()
    {
        if (cameraComponent == null)
            cameraComponent = GetComponent<Camera>();

        if (cameraComponent != null)
            CalculateCameraPosition();
    }
    */

    [ContextMenu("Calculate Camera Position")]
    public void CalculateCameraPosition()
    {
        // Get values from the camera component
        float cameraY = transform.position.y;
        float cameraXRotation = transform.eulerAngles.x;
        float fieldOfView = cameraComponent.fieldOfView; // degrees (vertical FOV)

        // Handle angle wrapping (Unity returns 0-360, we want -180 to 180)
        if (cameraXRotation > 180f)
            cameraXRotation -= 360f;

        Debug.Log($"Reading from camera - Y: {cameraY:F2}, X Rotation: {cameraXRotation:F2}, FOV: {fieldOfView:F2}");

        // Convert to radians
        float rotationRadians = cameraXRotation * Mathf.Deg2Rad;
        float fovRadians = fieldOfView * Mathf.Deg2Rad;

        // Calculate base Z position (center of view hits rig)
        float baseCameraZ = cameraY / Mathf.Tan(rotationRadians);

        // Calculate the perspective correction factor
        // The bottom of the view is further from camera than the top
        float halfFov = fovRadians / 2f;

        // Distance to top edge of view at ground level
        float topEdgeAngle = rotationRadians - halfFov;
        float distanceToTopEdge = cameraY / Mathf.Tan(topEdgeAngle);

        // Distance to bottom edge of view at ground level
        float bottomEdgeAngle = rotationRadians + halfFov;
        float distanceToBottomEdge = cameraY / Mathf.Tan(bottomEdgeAngle);

        // Calculate the actual buffer distances
        float topBufferDistance = distanceToTopEdge - baseCameraZ;
        float bottomBufferDistance = baseCameraZ - distanceToBottomEdge;

        // Calculate the ratio between top and bottom buffer distances
        float bufferRatio = topBufferDistance / bottomBufferDistance;

        // Calculate the corresponding bottom buffer for the specified top buffer
        float correspondingBottomBuffer = topBuffer / bufferRatio;

        Debug.Log($"Set camera Z: {baseCameraZ:F2}");
        /* Debug.Log($"Natural top buffer distance: {topBufferDistance:F2}");
        Debug.Log($"Natural bottom buffer distance: {bottomBufferDistance:F2}");
        Debug.Log($"Buffer ratio (top/bottom): {bufferRatio:F2}"); */
        Debug.Log($"Apply top buffer: {topBuffer:F2}");
        Debug.Log($"Apply bottom buffer: {correspondingBottomBuffer:F2}");
    }
}